require 'rails_helper'
include CompanyUserHelper

RSpec.describe BulkDiscoveredUserImportsController, type: :controller do
  create_company_and_user

  let(:discovered_user1) { create(:discovered_user, company_id: company.id) }
  let(:discovered_user2) { create(:discovered_user, company_id: company.id) }
  let(:default_form) { company.custom_forms.find_by(default: true, company_module: 'company_user') }

  describe "Bulk functionality of discovered users import" do
    context "#create" do
      it "should respond with status 400 if params are empty" do
        post :create, params: {discovered_users: []}

        expect(response.status).to eq(400)
      end

      it "should import users" do
        user_1_params = JSON.parse(discovered_user1.to_json)
        user_2_params = JSON.parse(discovered_user2.to_json)
        user_1_params['custom_form_id'] = default_form.id
        user_2_params['custom_form_id'] = default_form.id
        valid_params = [user_1_params, user_2_params]
        post :create, params: {discovered_users: valid_params}, as: :json
        saved_discovered1 = DiscoveredUser.find_by(email: discovered_user1.email)
        saved_discovered2 = DiscoveredUser.find_by(email: discovered_user2.email)

        expect(response.status).to eq(200)
        expect(saved_discovered1.status).to eq("imported")
        expect(saved_discovered2.status).to eq("imported")
      end

      it "should import users even if the users exist" do
        user_1_params = JSON.parse(discovered_user1.to_json)
        user_2_params = JSON.parse(discovered_user2.to_json)
        user_1_params['custom_form_id'] = default_form.id
        user_2_params['custom_form_id'] = default_form.id
        valid_params = [user_1_params, user_2_params]
        post :create, params: {discovered_users: valid_params}, as: :json

        expect(response.status).to eq(200)
        expect(discovered_user1.reload.status).to eq("imported")
        expect(discovered_user2.reload.status).to eq("imported")
      end
    end
  end
end
