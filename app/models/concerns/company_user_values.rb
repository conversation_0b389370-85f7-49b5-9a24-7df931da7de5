module CompanyUserValues
  extend ActiveSupport::Concern

  included do
    def user_avatar
      custom_form_values.joins(:custom_form_field).where(custom_form_fields: { field_attribute_type: "avatar" }).order('order_position').first
    end

    def mobile_phone
      if @mobile_phone.blank?
        @mobile_phone = begin
          cfv = custom_form_values.includes(:custom_form_field).find { |value| value.custom_form_field.name == "mobile_phone" }&.value_str
          if cfv.present? && cfv.split(',')[1].present?
            cfv
          else
            ''
          end
        end
      end
      @mobile_phone
    end

    def location_ids
      custom_form_values.joins(:custom_form_field).where(custom_form_fields: { field_attribute_type: "location_list" }).pluck(:value_int).uniq
    end

    def title
      custom_form_values.includes(:custom_form_field).find { |value| value.custom_form_field.name == "title" }&.value_str
    end

    def department
      custom_form_values.includes(:custom_form_field).find { |value| value.custom_form_field.name == "department" }&.value_str
    end

    def supervisor_id
      custom_form_values.includes(:custom_form_field).find { |value| value.custom_form_field.name == "supervisor" }&.value_int
    end

    def creator_contributor
      nil
    end
  end
end
